import { useRef, useCallback, useEffect, useState } from 'react'
import { v4 as uuidv4 } from 'uuid'
import './App.css'

// Import components
import {
  usePDFHandler,
  useAnnotationsHandler,
  useCanvasHandler,
  useExportUtils,
  useCSVHandler,
  Canvas,
  Toolbar,
  AnnotationsList,
  RoomNameDropdown
} from './components'

function App() {
  const fileInputRef = useRef(null)
  const csvInputRef = useRef(null)

  // Use custom hooks for different functionalities
  const {
    pdfFiles,
    currentPdfIndex,
    pdfPages,
    currentPageIndex,
    originalPdfDimensions,
    allPdfData,
    handleFileUpload,
    switchToPdf,
    setCurrentPageIndex,
    canvasToPdfCoordinates
  } = usePDFHandler()

  const {
    roomNames,
    csvFileName,
    handleCSVUpload,
    clearCSVData
  } = useCSVHandler()

  const {
    annotations,
    drawingMode,
    currentAnnotation,
    selectedAnnotation,
    isDragging,
    dragOffset,
    copiedAnnotation,
    polygonPoints,
    rectangleStartPoint,
    setDrawingMode,
    setCurrentAnnotation,
    setSelectedAnnotation,
    setIsDragging,
    setDragOffset,
    setCopiedAnnotation,
    setPolygonPoints,
    setRectangleStartPoint,
    getCurrentAnnotations,
    updateCurrentAnnotations,
    findAnnotationAtPoint,
    createRectangleAnnotation,
    finishPolygon,
    copyAnnotation,
    deleteAnnotation,
    updateAnnotationLabel,
    checkForOverlaps,
    clearDrawingState
  } = useAnnotationsHandler(currentPdfIndex, currentPageIndex)

  const {
    zoom,
    canvasOffset,
    showZoomIndicator,
    canvasRef,
    screenToCanvasCoordinates,
    autoFitToScreen,
    resetCanvasPosition,
    setZoom,
    handlePanning,
    handlePanMove,
    handlePanEnd
  } = useCanvasHandler(pdfPages, currentPageIndex)

  const { exportAnnotations, exportAnnotatedPDF } = useExportUtils(
    allPdfData,
    currentPdfIndex,
    originalPdfDimensions,
    canvasToPdfCoordinates
  )

  // Room name dropdown state
  const [showRoomDropdown, setShowRoomDropdown] = useState(false)
  const [dropdownPosition, setDropdownPosition] = useState({ x: 0, y: 0 })
  const [pendingAnnotation, setPendingAnnotation] = useState(null)

  // Handle drawing mode changes
  const handleDrawingModeChange = useCallback((mode) => {
    setDrawingMode(mode)
    clearDrawingState()
  }, [setDrawingMode, clearDrawingState])

  // Wrapper functions for toolbar actions
  const handleZoomChange = useCallback((newZoom) => {
    setZoom(newZoom)
  }, [setZoom])

  const handleAutoFit = useCallback(() => {
    autoFitToScreen()
  }, [autoFitToScreen])

  const handleResetPosition = useCallback(() => {
    resetCanvasPosition()
  }, [resetCanvasPosition])

  // Room name dropdown handlers
  const handleRoomSelection = useCallback((roomName) => {
    if (pendingAnnotation) {
      // Update the annotation with the selected room name
      const updatedAnnotation = {
        ...pendingAnnotation,
        label: roomName,
        roomName: roomName
      }

      // Add the annotation to the current annotations
      const currentAnnotations = getCurrentAnnotations()
      updateCurrentAnnotations([...currentAnnotations, updatedAnnotation])

      // Clear pending state
      setPendingAnnotation(null)
      setShowRoomDropdown(false)
    }
  }, [pendingAnnotation, getCurrentAnnotations, updateCurrentAnnotations])

  const handleRoomDropdownCancel = useCallback(() => {
    setPendingAnnotation(null)
    setShowRoomDropdown(false)
  }, [])

  // Show room dropdown after annotation creation
  const showRoomDropdownForAnnotation = useCallback((annotation, mouseEvent) => {
    console.log('showRoomDropdownForAnnotation called:', { annotation, roomNamesLength: roomNames.length })

    if (roomNames.length === 0) {
      // No CSV loaded, proceed with default annotation creation
      console.log('No CSV, adding annotation directly')
      const currentAnnotations = getCurrentAnnotations()
      updateCurrentAnnotations([...currentAnnotations, annotation])
      return
    }

    // Set up dropdown
    console.log('Setting up room dropdown')
    setPendingAnnotation(annotation)
    setDropdownPosition({
      x: mouseEvent.clientX + 10,
      y: mouseEvent.clientY + 10
    })
    setShowRoomDropdown(true)
    console.log('Room dropdown state set to true')
  }, [roomNames.length, getCurrentAnnotations, updateCurrentAnnotations])

  // Mouse event handlers
  const handleMouseDown = useCallback((event) => {
    // Handle panning first
    const panHandled = handlePanning(event, drawingMode)
    if (panHandled) return

    const canvasCoords = screenToCanvasCoordinates(event.clientX, event.clientY)

    // Check if clicking on existing annotation for selection/dragging
    const clickedAnnotation = findAnnotationAtPoint(canvasCoords)

    if (clickedAnnotation && (drawingMode === 'select' || drawingMode !== 'hand')) {
      // Select annotation and prepare for dragging (only allow dragging in select mode)
      setSelectedAnnotation(clickedAnnotation)

      if (drawingMode === 'select') {
        setIsDragging(true)

        if (clickedAnnotation.type === 'rectangle') {
          setDragOffset({
            x: canvasCoords.x - clickedAnnotation.x,
            y: canvasCoords.y - clickedAnnotation.y
          })
        } else if (clickedAnnotation.type === 'polygon') {
          // For polygons, use the first point as reference
          setDragOffset({
            x: canvasCoords.x - clickedAnnotation.points[0].x,
            y: canvasCoords.y - clickedAnnotation.points[0].y
          })
        }
      }
      return
    }

    // Handle drawing based on mode
    if (drawingMode === 'rectangle') {
      if (!rectangleStartPoint) {
        // First click - set start point (store in canvas coordinates)
        setRectangleStartPoint(canvasCoords)
      } else {
        // Second click - create rectangle (store in canvas coordinates)
        console.log('Creating rectangle with roomNames.length:', roomNames.length)
        if (roomNames.length > 0) {
          // CSV loaded, use room dropdown
          console.log('Using room dropdown for rectangle')
          createRectangleAnnotation(rectangleStartPoint, canvasCoords, (annotation) => {
            console.log('Rectangle callback called with annotation:', annotation)
            showRoomDropdownForAnnotation(annotation, event)
          })
        } else {
          // No CSV loaded, create rectangle normally
          console.log('Creating rectangle normally (no CSV)')
          createRectangleAnnotation(rectangleStartPoint, canvasCoords)
        }
        setRectangleStartPoint(null)
        setSelectedAnnotation(null)
      }
    }
  }, [drawingMode, handlePanning, screenToCanvasCoordinates, findAnnotationAtPoint, rectangleStartPoint, createRectangleAnnotation, setSelectedAnnotation, setIsDragging, setDragOffset, setRectangleStartPoint, showRoomDropdownForAnnotation, roomNames.length])

  const handleMouseMove = useCallback((event) => {
    // Handle panning first
    const panHandled = handlePanMove(event)
    if (panHandled) return

    const canvasCoords = screenToCanvasCoordinates(event.clientX, event.clientY)

    // Handle dragging selected annotation (only in select mode)
    if (isDragging && selectedAnnotation && drawingMode === 'select') {
      const newX = canvasCoords.x - dragOffset.x
      const newY = canvasCoords.y - dragOffset.y

      const currentAnnotations = getCurrentAnnotations()
      const updatedAnnotations = currentAnnotations.map(annotation => {
        if (annotation.id === selectedAnnotation.id) {
          if (annotation.type === 'rectangle') {
            return {
              ...annotation,
              x: newX,
              y: newY
            }
          } else if (annotation.type === 'polygon') {
            const deltaX = newX - annotation.points[0].x
            const deltaY = newY - annotation.points[0].y
            return {
              ...annotation,
              points: annotation.points.map(point => ({
                x: point.x + deltaX,
                y: point.y + deltaY
              }))
            }
          }
        }
        return annotation
      })
      updateCurrentAnnotations(updatedAnnotations)

      // Update selected annotation reference
      setSelectedAnnotation(prev => {
        if (prev.type === 'rectangle') {
          return { ...prev, x: newX, y: newY }
        } else if (prev.type === 'polygon') {
          const deltaX = newX - prev.points[0].x
          const deltaY = newY - prev.points[0].y
          return {
            ...prev,
            points: prev.points.map(point => ({
              x: point.x + deltaX,
              y: point.y + deltaY
            }))
          }
        }
        return prev
      })
    }
  }, [handlePanMove, isDragging, selectedAnnotation, drawingMode, dragOffset, screenToCanvasCoordinates, getCurrentAnnotations, updateCurrentAnnotations, setSelectedAnnotation])

  const handleMouseUp = useCallback(() => {
    handlePanEnd()
    setIsDragging(false)
  }, [handlePanEnd, setIsDragging])

  const handleCanvasClick = useCallback((event) => {
    // Prevent default click behavior when dragging or panning
    if (isDragging) return

    // Hand mode and select mode don't handle clicks for annotation creation
    if (drawingMode === 'hand' || drawingMode === 'select') return

    const canvasCoords = screenToCanvasCoordinates(event.clientX, event.clientY)

    if (drawingMode === 'polygon') {
      // Check if clicking on existing annotation first
      const clickedAnnotation = findAnnotationAtPoint(canvasCoords)
      if (clickedAnnotation) {
        setSelectedAnnotation(clickedAnnotation)
        return
      }

      if (!currentAnnotation) {
        // Start new polygon (store in canvas coordinates)
        const currentAnnotations = getCurrentAnnotations()
        const annotationNumber = currentAnnotations.length + 1
        setCurrentAnnotation({
          id: uuidv4(),
          type: 'polygon',
          pageIndex: currentPageIndex,
          points: [canvasCoords],
          color: '#ff0000',
          label: `Polygon ${annotationNumber}`
        })
        setPolygonPoints([canvasCoords])
        setSelectedAnnotation(null)
      } else {
        // Add point to current polygon
        const newPoints = [...polygonPoints, canvasCoords]
        setPolygonPoints(newPoints)
        setCurrentAnnotation(prev => ({
          ...prev,
          points: newPoints
        }))
      }
    } else if (drawingMode === 'rectangle') {
      // For rectangle mode, clear selection if clicking on empty space
      const clickedAnnotation = findAnnotationAtPoint(canvasCoords)
      if (!clickedAnnotation && !rectangleStartPoint) {
        setSelectedAnnotation(null)
      }
    }
  }, [drawingMode, screenToCanvasCoordinates, currentAnnotation, polygonPoints, currentPageIndex, isDragging, findAnnotationAtPoint, rectangleStartPoint, setCurrentAnnotation, setPolygonPoints, setSelectedAnnotation])

  // Keyboard shortcuts handler
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Only handle shortcuts if not typing in an input field or editing annotation labels
      const activeElement = document.activeElement
      if (event.target.tagName === 'INPUT' ||
          event.target.tagName === 'TEXTAREA' ||
          event.target.classList.contains('annotation-label-input') ||
          event.target.dataset.editingLabel === 'true' ||
          event.target.contentEditable === 'true' ||
          (activeElement && activeElement.tagName === 'INPUT') ||
          (activeElement && activeElement.classList.contains('annotation-label-input'))) {
        return
      }

      if (event.ctrlKey && event.key === 's') {
        event.preventDefault()
        exportAnnotations(annotations)
      } else if (event.ctrlKey && event.key === 'c') {
        event.preventDefault()
        if (selectedAnnotation) {
          copyAnnotation(selectedAnnotation)
        }
      } else if (event.ctrlKey && event.key === 'v') {
        event.preventDefault()
        if (copiedAnnotation) {
          const currentAnnotations = getCurrentAnnotations()
          const annotationNumber = currentAnnotations.length + 1
          const newAnnotation = {
            ...copiedAnnotation,
            id: uuidv4(),
            pageIndex: currentPageIndex,
            label: copiedAnnotation.label ? `${copiedAnnotation.label} (Copy)` : `${copiedAnnotation.type} ${annotationNumber}`
          }

          // Offset the pasted annotation slightly
          if (newAnnotation.type === 'rectangle') {
            newAnnotation.x += 20
            newAnnotation.y += 20
          } else if (newAnnotation.type === 'polygon') {
            newAnnotation.points = newAnnotation.points.map(point => ({
              x: point.x + 20,
              y: point.y + 20
            }))
          }

          // No overlap check for paste operations since they're intentionally offset from the original
          updateCurrentAnnotations([...getCurrentAnnotations(), newAnnotation])
          setSelectedAnnotation(newAnnotation)
        }
      } else if (event.key === 'Delete' || event.key === 'Backspace') {
        event.preventDefault()
        if (selectedAnnotation) {
          deleteAnnotation(selectedAnnotation.id)
          setSelectedAnnotation(null)
        }
      } else if (event.key === 'Enter') {
        event.preventDefault()
        if (currentAnnotation && drawingMode === 'polygon' && polygonPoints.length >= 3) {
          if (roomNames.length > 0) {
            // Use room dropdown for polygon
            finishPolygon((annotation) => {
              // Create a synthetic mouse event for positioning
              const syntheticEvent = {
                clientX: window.innerWidth / 2,
                clientY: window.innerHeight / 2
              }
              showRoomDropdownForAnnotation(annotation, syntheticEvent)
            })
          } else {
            // No CSV loaded, finish normally
            finishPolygon()
          }
        }
      } else if (event.key === 'h' || event.key === 'H') {
        handleDrawingModeChange('hand')
      } else if (event.key === 'r' || event.key === 'R') {
        handleDrawingModeChange('rectangle')
      } else if (event.key === 'p' || event.key === 'P') {
        handleDrawingModeChange('polygon')
      } else if (event.key === 's' || event.key === 'S') {
        handleDrawingModeChange('select')
      } else if (event.key === 'Escape') {
        clearDrawingState()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [exportAnnotations, annotations, selectedAnnotation, copiedAnnotation, currentPageIndex, currentAnnotation, drawingMode, finishPolygon, polygonPoints.length, copyAnnotation, updateCurrentAnnotations, getCurrentAnnotations, setSelectedAnnotation, deleteAnnotation, handleDrawingModeChange, clearDrawingState, roomNames.length, showRoomDropdownForAnnotation])

  return (
    <div className="app">
      <Toolbar
        fileInputRef={fileInputRef}
        onFileUpload={handleFileUpload}
        csvInputRef={csvInputRef}
        onCSVUpload={handleCSVUpload}
        csvFileName={csvFileName}
        roomNames={roomNames}
        allPdfData={allPdfData}
        currentPdfIndex={currentPdfIndex}
        onSwitchToPdf={switchToPdf}
        pdfPages={pdfPages}
        currentPageIndex={currentPageIndex}
        onSetCurrentPageIndex={setCurrentPageIndex}
        zoom={zoom}
        onSetZoom={handleZoomChange}
        onAutoFitToScreen={handleAutoFit}
        onResetCanvasPosition={handleResetPosition}
        drawingMode={drawingMode}
        onSetDrawingMode={handleDrawingModeChange}
        currentAnnotation={currentAnnotation}
        onFinishPolygon={(onAnnotationCreated) => finishPolygon(onAnnotationCreated)}
        rectangleStartPoint={rectangleStartPoint}
        onSetRectangleStartPoint={setRectangleStartPoint}
        onExportAnnotations={() => exportAnnotations(annotations)}
        onExportAnnotatedPDF={() => exportAnnotatedPDF(annotations)}
        getCurrentAnnotations={getCurrentAnnotations}
        selectedAnnotation={selectedAnnotation}
        onCopyAnnotation={copyAnnotation}
        onDeleteAnnotation={deleteAnnotation}
        showRoomDropdownForAnnotation={showRoomDropdownForAnnotation}
      />

      <div className="main-content">
        <Canvas
          pdfPages={pdfPages}
          currentPageIndex={currentPageIndex}
          annotations={getCurrentAnnotations()}
          selectedAnnotation={selectedAnnotation}
          currentAnnotation={currentAnnotation}
          polygonPoints={polygonPoints}
          rectangleStartPoint={rectangleStartPoint}
          drawingMode={drawingMode}
          zoom={zoom}
          canvasOffset={canvasOffset}
          showZoomIndicator={showZoomIndicator}
          canvasRef={canvasRef}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onClick={handleCanvasClick}
          onContextMenu={(e) => {
            e.preventDefault()
            // Handle right-click context menu if needed
          }}
        />

        {getCurrentAnnotations().length > 0 && (
          <AnnotationsList
            annotations={getCurrentAnnotations()}
            selectedAnnotation={selectedAnnotation}
            onSelectAnnotation={setSelectedAnnotation}
            onCopyAnnotation={copyAnnotation}
            onDeleteAnnotation={deleteAnnotation}
            onUpdateAnnotationLabel={updateAnnotationLabel}
            onCheckForOverlaps={checkForOverlaps}
          />
        )}
      </div>

      <RoomNameDropdown
        roomNames={roomNames}
        position={dropdownPosition}
        onSelectRoom={handleRoomSelection}
        onCancel={handleRoomDropdownCancel}
        isVisible={showRoomDropdown}
      />

      {/* Temporary debug info */}
      <div style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        background: 'lime',
        padding: '10px',
        fontSize: '12px',
        zIndex: 2000,
        border: '2px solid black'
      }}>
        <div>showRoomDropdown: {showRoomDropdown ? 'TRUE' : 'FALSE'}</div>
        <div>roomNames.length: {roomNames.length}</div>
        <div>dropdownPosition: {dropdownPosition.x}, {dropdownPosition.y}</div>
        <div>pendingAnnotation: {pendingAnnotation ? 'EXISTS' : 'NULL'}</div>
      </div>

    </div>
  )
}

export default App
