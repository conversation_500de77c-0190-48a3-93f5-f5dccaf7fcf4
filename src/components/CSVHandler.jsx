import { useState, useCallback } from 'react'

export const useCSVHandler = () => {
  const [roomNames, setRoomNames] = useState([])
  const [csvFileName, setCsvFileName] = useState('')

  // Parse CSV content and extract room names from the last column
  const parseCSV = useCallback((csvContent) => {
    try {
      const lines = csvContent.split('\n').filter(line => line.trim() !== '')
      const roomNamesSet = new Set()

      lines.forEach(line => {
        // Split by comma and handle quoted values
        const columns = line.split(',').map(col => col.trim().replace(/^"|"$/g, ''))
        
        // Skip rows with less than 2 columns
        if (columns.length < 2) return
        
        // Get the last column (room name)
        const roomName = columns[columns.length - 1].trim()
        
        // Skip empty room names or rows with NaN/null values
        if (roomName && 
            roomName !== '' && 
            roomName.toLowerCase() !== 'nan' && 
            roomName.toLowerCase() !== 'null' &&
            roomName.toLowerCase() !== 'undefined') {
          roomNamesSet.add(roomName)
        }
      })

      return Array.from(roomNamesSet).sort()
    } catch (error) {
      console.error('Error parsing CSV:', error)
      return []
    }
  }, [])

  // Handle CSV file upload
  const handleCSVUpload = useCallback(async (event) => {
    const file = event.target.files[0]
    if (!file) return

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.csv')) {
      alert('Please select a CSV file')
      return
    }

    try {
      const text = await file.text()
      const extractedRoomNames = parseCSV(text)
      
      if (extractedRoomNames.length === 0) {
        alert('No valid room names found in the CSV file. Please check that the last column contains room names and there are no NaN values.')
        return
      }

      setRoomNames(extractedRoomNames)
      setCsvFileName(file.name)
      
      console.log(`Loaded ${extractedRoomNames.length} room names from ${file.name}:`, extractedRoomNames)
      
    } catch (error) {
      console.error('Error reading CSV file:', error)
      alert('Error reading CSV file. Please make sure it\'s a valid CSV file.')
    }
  }, [parseCSV])

  // Clear CSV data
  const clearCSVData = useCallback(() => {
    setRoomNames([])
    setCsvFileName('')
  }, [])

  return {
    roomNames,
    csvFileName,
    handleCSVUpload,
    clearCSVData
  }
}
