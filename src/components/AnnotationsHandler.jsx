import { useState, useCallback } from 'react'
import { v4 as uuidv4 } from 'uuid'

export const useAnnotationsHandler = (currentPdfIndex, currentPageIndex) => {
  const [annotations, setAnnotations] = useState({}) // Annotations grouped by PDF index and page
  const [drawingMode, setDrawingMode] = useState('rectangle') // 'rectangle', 'polygon', or 'hand'
  const [currentAnnotation, setCurrentAnnotation] = useState(null)
  const [selectedAnnotation, setSelectedAnnotation] = useState(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [copiedAnnotation, setCopiedAnnotation] = useState(null)
  const [polygonPoints, setPolygonPoints] = useState([])
  const [rectangleStartPoint, setRectangleStartPoint] = useState(null)

  // Get current PDF annotations for current page
  const getCurrentAnnotations = useCallback(() => {
    const key = `${currentPdfIndex}-${currentPageIndex}`
    return annotations[key] || []
  }, [annotations, currentPdfIndex, currentPageIndex])

  // Update annotations for current PDF and page
  const updateCurrentAnnotations = useCallback((newAnnotations) => {
    const key = `${currentPdfIndex}-${currentPageIndex}`
    setAnnotations(prev => ({
      ...prev,
      [key]: newAnnotations
    }))
  }, [currentPdfIndex, currentPageIndex])

  // Check if point is inside rectangle
  const isPointInRectangle = useCallback((point, rect) => {
    return point.x >= rect.x &&
           point.x <= rect.x + rect.width &&
           point.y >= rect.y &&
           point.y <= rect.y + rect.height
  }, [])

  // Check if point is inside polygon using ray casting algorithm
  const isPointInPolygon = useCallback((point, polygon) => {
    let inside = false
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      if (((polygon[i].y > point.y) !== (polygon[j].y > point.y)) &&
          (point.x < (polygon[j].x - polygon[i].x) * (point.y - polygon[i].y) / (polygon[j].y - polygon[i].y) + polygon[i].x)) {
        inside = !inside
      }
    }
    return inside
  }, [])

  // Find annotation at given point
  const findAnnotationAtPoint = useCallback((point) => {
    const currentAnnotations = getCurrentAnnotations()
    for (let i = currentAnnotations.length - 1; i >= 0; i--) {
      const annotation = currentAnnotations[i]

      if (annotation.type === 'rectangle') {
        if (isPointInRectangle(point, annotation)) {
          return annotation
        }
      } else if (annotation.type === 'polygon') {
        if (isPointInPolygon(point, annotation.points)) {
          return annotation
        }
      }
    }
    return null
  }, [getCurrentAnnotations, isPointInRectangle, isPointInPolygon])

  // Check if two rectangles overlap (with small tolerance for edge touching)
  const doRectanglesOverlap = useCallback((rect1, rect2) => {
    const tolerance = 1 // Small tolerance to avoid false positives for edge touching
    return !(rect1.x + rect1.width <= rect2.x + tolerance ||
             rect2.x + rect2.width <= rect1.x + tolerance ||
             rect1.y + rect1.height <= rect2.y + tolerance ||
             rect2.y + rect2.height <= rect1.y + tolerance)
  }, [])

  // Check if rectangle overlaps with polygon
  const doesRectangleOverlapPolygon = useCallback((rect, polygon) => {
    // Check if any corner of rectangle is inside polygon
    const corners = [
      { x: rect.x, y: rect.y },
      { x: rect.x + rect.width, y: rect.y },
      { x: rect.x, y: rect.y + rect.height },
      { x: rect.x + rect.width, y: rect.y + rect.height }
    ]

    for (const corner of corners) {
      if (isPointInPolygon(corner, polygon.points)) {
        return true
      }
    }

    // Check if any polygon point is inside rectangle
    for (const point of polygon.points) {
      if (isPointInRectangle(point, rect)) {
        return true
      }
    }

    return false
  }, [isPointInPolygon, isPointInRectangle])

  // Check if two polygons overlap (simplified check)
  const doPolygonsOverlap = useCallback((poly1, poly2) => {
    // Check if any point of poly1 is inside poly2
    for (const point of poly1.points) {
      if (isPointInPolygon(point, poly2.points)) {
        return true
      }
    }

    // Check if any point of poly2 is inside poly1
    for (const point of poly2.points) {
      if (isPointInPolygon(point, poly1.points)) {
        return true
      }
    }

    return false
  }, [isPointInPolygon])

  // Check for overlaps with existing annotations
  const checkForOverlaps = useCallback((newAnnotation) => {
    const currentAnnotations = getCurrentAnnotations()
    const overlappingAnnotations = []

    for (const existingAnnotation of currentAnnotations) {
      if (existingAnnotation.id === newAnnotation.id) continue

      let hasOverlap = false

      if (newAnnotation.type === 'rectangle' && existingAnnotation.type === 'rectangle') {
        hasOverlap = doRectanglesOverlap(newAnnotation, existingAnnotation)
      } else if (newAnnotation.type === 'rectangle' && existingAnnotation.type === 'polygon') {
        hasOverlap = doesRectangleOverlapPolygon(newAnnotation, existingAnnotation)
      } else if (newAnnotation.type === 'polygon' && existingAnnotation.type === 'rectangle') {
        hasOverlap = doesRectangleOverlapPolygon(existingAnnotation, newAnnotation)
      } else if (newAnnotation.type === 'polygon' && existingAnnotation.type === 'polygon') {
        hasOverlap = doPolygonsOverlap(newAnnotation, existingAnnotation)
      }

      if (hasOverlap) {
        overlappingAnnotations.push(existingAnnotation)
      }
    }

    return overlappingAnnotations
  }, [getCurrentAnnotations, doRectanglesOverlap, doesRectangleOverlapPolygon, doPolygonsOverlap])

  // Create rectangle annotation
  const createRectangleAnnotation = useCallback((startPoint, endPoint) => {
    const startX = Math.min(startPoint.x, endPoint.x)
    const startY = Math.min(startPoint.y, endPoint.y)
    const width = Math.abs(endPoint.x - startPoint.x)
    const height = Math.abs(endPoint.y - startPoint.y)

    // Only create rectangle if it has meaningful dimensions
    if (width > 5 && height > 5) {
      const currentAnnotations = getCurrentAnnotations()
      const annotationNumber = currentAnnotations.length + 1
      const newAnnotation = {
        id: uuidv4(),
        type: 'rectangle',
        pageIndex: currentPageIndex,
        x: startX,
        y: startY,
        width: width,
        height: height,
        color: '#ff0000',
        label: `Rectangle ${annotationNumber}`
      }

      // Check for overlaps
      const overlappingAnnotations = checkForOverlaps(newAnnotation)
      if (overlappingAnnotations.length > 0) {
        const overlappingLabels = overlappingAnnotations.map(ann => ann.label || `${ann.type} ${ann.id.slice(0, 8)}`).join(', ')
        const proceed = window.confirm(
          `Warning: This rectangle overlaps with existing annotation(s): ${overlappingLabels}\n\n` +
          `In this application, no area should be shared by more than one annotation. ` +
          `Do you want to create this overlapping annotation anyway?`
        )

        if (!proceed) {
          return null
        }
      }

      updateCurrentAnnotations([...currentAnnotations, newAnnotation])
      return newAnnotation
    }
    return null
  }, [currentPageIndex, getCurrentAnnotations, updateCurrentAnnotations, checkForOverlaps])

  // Finish polygon annotation
  const finishPolygon = useCallback(() => {
    if (currentAnnotation && polygonPoints.length >= 3) {
      // Check for overlaps
      const overlappingAnnotations = checkForOverlaps(currentAnnotation)
      if (overlappingAnnotations.length > 0) {
        const overlappingLabels = overlappingAnnotations.map(ann => ann.label || `${ann.type} ${ann.id.slice(0, 8)}`).join(', ')
        const proceed = window.confirm(
          `Warning: This polygon overlaps with existing annotation(s): ${overlappingLabels}\n\n` +
          `In this application, no area should be shared by more than one annotation. ` +
          `Do you want to create this overlapping annotation anyway?`
        )

        if (!proceed) {
          setCurrentAnnotation(null)
          setPolygonPoints([])
          return false
        }
      }

      updateCurrentAnnotations([...getCurrentAnnotations(), currentAnnotation])
      setCurrentAnnotation(null)
      setPolygonPoints([])
      return true
    }
    return false
  }, [currentAnnotation, polygonPoints, getCurrentAnnotations, updateCurrentAnnotations, checkForOverlaps])

  // Copy annotation functionality
  const copyAnnotation = useCallback((annotation) => {
    setCopiedAnnotation({ ...annotation, id: uuidv4() })
  }, [])

  // Delete annotation
  const deleteAnnotation = useCallback((annotationId) => {
    try {
      const currentAnnotations = getCurrentAnnotations()
      if (!Array.isArray(currentAnnotations)) {
        console.error('getCurrentAnnotations() did not return an array:', currentAnnotations)
        return
      }

      const updatedAnnotations = currentAnnotations.filter(ann => ann.id !== annotationId)
      updateCurrentAnnotations(updatedAnnotations)

      // Clear selected annotation if it was the one being deleted
      if (selectedAnnotation && selectedAnnotation.id === annotationId) {
        setSelectedAnnotation(null)
      }
    } catch (error) {
      console.error('Error deleting annotation:', error)
    }
  }, [getCurrentAnnotations, updateCurrentAnnotations, selectedAnnotation])

  // Update annotation label
  const updateAnnotationLabel = useCallback((annotationId, newLabel) => {
    const currentAnnotations = getCurrentAnnotations()
    const updatedAnnotations = currentAnnotations.map(annotation =>
      annotation.id === annotationId
        ? { ...annotation, label: newLabel }
        : annotation
    )
    updateCurrentAnnotations(updatedAnnotations)

    // Update selected annotation if it's the one being edited
    if (selectedAnnotation && selectedAnnotation.id === annotationId) {
      setSelectedAnnotation(prev => ({ ...prev, label: newLabel }))
    }
  }, [getCurrentAnnotations, updateCurrentAnnotations, selectedAnnotation])

  // Clear current drawing state
  const clearDrawingState = useCallback(() => {
    setCurrentAnnotation(null)
    setPolygonPoints([])
    setRectangleStartPoint(null)
    setSelectedAnnotation(null)
    setIsDragging(false)
  }, [])

  return {
    // State
    annotations,
    drawingMode,
    currentAnnotation,
    selectedAnnotation,
    isDragging,
    dragOffset,
    copiedAnnotation,
    polygonPoints,
    rectangleStartPoint,

    // Actions
    setDrawingMode,
    setCurrentAnnotation,
    setSelectedAnnotation,
    setIsDragging,
    setDragOffset,
    setCopiedAnnotation,
    setPolygonPoints,
    setRectangleStartPoint,
    getCurrentAnnotations,
    updateCurrentAnnotations,
    findAnnotationAtPoint,
    createRectangleAnnotation,
    finishPolygon,
    copyAnnotation,
    deleteAnnotation,
    updateAnnotationLabel,
    checkForOverlaps,
    clearDrawingState
  }
}
