import React, { useState, useEffect, useRef } from 'react'

const RoomNameDropdown = ({
  roomNames,
  position,
  onSelectRoom,
  onCancel,
  isVisible
}) => {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')
  const dropdownRef = useRef(null)
  const searchInputRef = useRef(null)

  // Filter room names based on search term
  const filteredRoomNames = roomNames.filter(name =>
    name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Reset selection when filtered list changes
  useEffect(() => {
    setSelectedIndex(0)
  }, [searchTerm])

  // Focus search input when dropdown becomes visible
  useEffect(() => {
    if (isVisible && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [isVisible])

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!isVisible) return

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault()
          setSelectedIndex(prev => 
            prev < filteredRoomNames.length - 1 ? prev + 1 : prev
          )
          break
        case 'ArrowUp':
          event.preventDefault()
          setSelectedIndex(prev => prev > 0 ? prev - 1 : prev)
          break
        case 'Enter':
          event.preventDefault()
          if (filteredRoomNames.length > 0) {
            onSelectRoom(filteredRoomNames[selectedIndex])
          }
          break
        case 'Escape':
          event.preventDefault()
          onCancel()
          break
        default:
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isVisible, selectedIndex, filteredRoomNames, onSelectRoom, onCancel])

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        onCancel()
      }
    }

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isVisible, onCancel])

  if (!isVisible || roomNames.length === 0) {
    return null
  }

  return (
    <div
      ref={dropdownRef}
      className="room-name-dropdown"
      style={{
        position: 'fixed',
        left: position.x,
        top: position.y,
        zIndex: 1000
      }}
    >
      <div className="room-dropdown-header">
        <h4>Assign Room Name</h4>
        <input
          ref={searchInputRef}
          type="text"
          placeholder="Search room names..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="room-search-input"
        />
      </div>
      
      <div className="room-dropdown-list">
        {filteredRoomNames.length > 0 ? (
          filteredRoomNames.map((roomName, index) => (
            <div
              key={roomName}
              className={`room-dropdown-item ${index === selectedIndex ? 'selected' : ''}`}
              onClick={() => onSelectRoom(roomName)}
              onMouseEnter={() => setSelectedIndex(index)}
            >
              {roomName}
            </div>
          ))
        ) : (
          <div className="room-dropdown-item no-results">
            No rooms found matching "{searchTerm}"
          </div>
        )}
      </div>
      
      <div className="room-dropdown-footer">
        <button onClick={onCancel} className="cancel-button">
          Cancel (Esc)
        </button>
        {filteredRoomNames.length > 0 && (
          <button 
            onClick={() => onSelectRoom(filteredRoomNames[selectedIndex])}
            className="select-button"
          >
            Select (Enter)
          </button>
        )}
      </div>
    </div>
  )
}

export default RoomNameDropdown
